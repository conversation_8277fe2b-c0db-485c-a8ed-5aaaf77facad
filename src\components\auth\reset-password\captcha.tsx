'use client'

import { useState, useEffect } from 'react'

interface CaptchaProps {
  value: string
  onChange: (value: string) => void
  error: string | null
  onCaptchaChange: (question: string, answer: string) => void
}

interface MathOperation {
  question: string
  answer: string
}

export const Captcha = ({ onChange, error, onCaptchaChange }: CaptchaProps) => {
  const [mathOperation, setMathOperation] = useState<MathOperation>({ question: '', answer: '' })

  const generateMathCaptcha = (): MathOperation => {
    const operations = ['+', '-', '*']
    const operation = operations[Math.floor(Math.random() * operations.length)]

    let num1: number, num2: number, result: number

    switch (operation) {
      case '+':
        num1 = Math.floor(Math.random() * 20) + 1 // 1-20
        num2 = Math.floor(Math.random() * 20) + 1 // 1-20
        result = num1 + num2
        break
      case '-':
        num1 = Math.floor(Math.random() * 30) + 10 // 10-39
        num2 = Math.floor(Math.random() * (num1 - 1)) + 1 // 1 até num1-1
        result = num1 - num2
        break
      case '*':
        num1 = Math.floor(Math.random() * 10) + 1 // 1-10
        num2 = Math.floor(Math.random() * 10) + 1 // 1-10
        result = num1 * num2
        break
      default:
        num1 = 5
        num2 = 3
        result = 8
    }

    return {
      question: `${num1} ${operation} ${num2} = ?`,
      answer: result.toString()
    }
  }

  const refreshCaptcha = () => {
    const newOperation = generateMathCaptcha()
    setMathOperation(newOperation)
    onCaptchaChange(newOperation.question, newOperation.answer)
    onChange('') // Limpar o input
  }

  useEffect(() => {
    // Gerar captcha inicial
    refreshCaptcha()
  }, [])

  useEffect(() => {
    // Atualizar o valor quando o captcha mudar
    if (mathOperation.question) {
      onCaptchaChange(mathOperation.question, mathOperation.answer)
    }
  }, [mathOperation])

  return (
    <div>
      <label
        htmlFor="captcha"
        className="block text-sm font-medium text-gray-900 dark:text-gray-200"
      >
        Digite o código abaixo
      </label>
      <div className="mt-1.5 space-y-3">
        <div className="flex items-center gap-3">
          <div className="flex h-12 items-center justify-center rounded-lg bg-gray-100 px-4 font-mono text-lg text-gray-900 dark:bg-gray-800 dark:text-white">
            {captchaValue}
          </div>
          <button
            type="button"
            onClick={refreshCaptcha}
            className="rounded-lg p-2 text-gray-500 transition-colors hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <svg
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
          </button>
        </div>
        <input
          id="captcha"
          name="captcha"
          type="text"
          placeholder="Digite o código"
          className={`block w-full rounded-lg border ${
            error ? 'border-red-500' : 'border-gray-200'
          } bg-white px-4 py-3 text-gray-900 transition-colors placeholder:text-gray-500 focus:tenant-primary-border focus:outline-none focus:ring-4 focus:tenant-primary-border/10 dark:border-gray-800 dark:bg-gray-900 dark:text-white dark:placeholder:text-gray-400 dark:focus:tenant-primary-border dark:focus:tenant-primary-border/10`}
        />
        {error && (
          <p className="mt-1.5 text-sm tenant-primary dark:tenant-primary">{error}</p>
        )}
      </div>
    </div>
  )
} 